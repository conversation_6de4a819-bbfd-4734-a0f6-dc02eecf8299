<script setup lang="ts">
import { Meta2d } from '@meta2d/core';
import jsonData from './jsonData.json';
import { ref, onMounted, onUnmounted, nextTick } from 'vue';

declare const window: any;
declare const meta2d: Meta2d;
const meta2dOptions = {
  rule: 0, //是否显示标尺
  //   hoverColor:"",//鼠标移动到画笔上的颜色
  //   disableScale:1,//禁止画布缩放
  //   disableTranslate:1,//禁止画布移动
  //   interval:0,//绘画帧时长
  //   activeColor:"",//画笔选中颜色
  //   hoverCursor:"",//鼠标经过画笔的样式
};
const meta2dInstance = ref(null);
const contextMenuVisible = ref(false);
function contextmenu() {
  contextMenuVisible.value = true;
}
function click() {
  contextMenuVisible.value = false;
}

onMounted(async () => {
  // 等待 DOM 渲染完成
  await nextTick();

  // 确保容器有尺寸后再初始化画布
  const container = document.getElementById('meta2d');
  if (container && container.offsetWidth > 0 && container.offsetHeight > 0) {
    // 初始化画布
    meta2dInstance.value = new Meta2d('meta2d', meta2dOptions);
    jsonData.rule = false;
    jsonData.locked = 1;
    // 示例：打开默认数据
    meta2d.open(jsonData);
    meta2d.fitView(true, 20);
    meta2d.centerView();
  } else {
    // 如果容器尺寸为0，延迟初始化
    setTimeout(() => {
      const retryContainer = document.getElementById('meta2d');
      if (
        retryContainer &&
        retryContainer.offsetWidth > 0 &&
        retryContainer.offsetHeight > 0
      ) {
        meta2dInstance.value = new Meta2d('meta2d', meta2dOptions);
        jsonData.rule = false;
        jsonData.locked = 1;
        meta2d.open(jsonData);
        meta2d.fitView(true, 20);
        meta2d.centerView();
      }
    }, 100);
  }

  // meta2d.register(flowPens());
  // meta2d.register(activityDiagram());
  // meta2d.register(classPens());
  // meta2d.register(sequencePens());
  // meta2d.registerCanvasDraw(sequencePensbyCtx());
  // meta2d.registerCanvasDraw(formPens());
  // // 监听消息事件
  // meta2d.on('contextmenu', contextmenu);
  // meta2d.on('click', click);
  // // 打开文件
  // meta2d.open(json);
});
onUnmounted(() => {
  if (meta2d) {
    // meta2d.off('contextmenu', contextmenu);
    // meta2d.off('click', click);
    meta2d.destroy();
  }
});
</script>
<template>
  <div id="meta2d" class="h-full w-full"></div>
</template>

<style scoped lang="scss">
#meta2d {

}
</style>
